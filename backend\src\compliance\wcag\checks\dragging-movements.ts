/**
 * WCAG Rule 13: Dragging Movements - 2.5.7
 * 70% Automated - Manual review for gesture alternatives and complex interactions
 */

import { Page } from 'puppeteer';
import {
  ManualReviewConfig,
  ManualReviewTemplate,
  ManualReviewItem,
} from '../utils/manual-review-template';
import { WcagEvidence } from '../types';

interface DragInteraction {
  element: string;
  type: 'drag-drop' | 'slider' | 'sortable' | 'resizable' | 'swipe';
  hasAlternative: boolean;
  alternativeMethod?: string;
  isEssential: boolean;
}

export class DraggingMovementsCheck {
  private checkTemplate = new ManualReviewTemplate();

  /**
   * Perform dragging movements check - 70% automated
   */
  async performCheck(config: ManualReviewConfig) {
    return this.checkTemplate.executeManualReviewCheck(
      'WCAG-013',
      'Dragging Movements',
      'operable',
      0.06,
      'AA',
      0.7, // 70% automation rate
      config,
      this.executeDraggingMovementsCheck.bind(this),
    );
  }

  /**
   * Execute comprehensive dragging movements analysis
   */
  private async executeDraggingMovementsCheck(page: Page, config: ManualReviewConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    // Analyze drag and drop elements
    const dragDropAnalysis = await this.analyzeDragDropElements(page);

    // Analyze sliders and range inputs
    const sliderAnalysis = await this.analyzeSliders(page);

    // Analyze sortable lists
    const sortableAnalysis = await this.analyzeSortableLists(page);

    // Analyze resizable elements
    const resizableAnalysis = await this.analyzeResizableElements(page);

    // Check for touch gestures
    const gestureAnalysis = await this.analyzeGestureInteractions(page);

    // Combine all analyses
    const allAnalyses = [
      dragDropAnalysis,
      sliderAnalysis,
      sortableAnalysis,
      resizableAnalysis,
      gestureAnalysis,
    ];

    let totalChecks = 0;
    let passedChecks = 0;
    let manualReviewCount = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      manualReviewCount += analysis.manualReviewItems.length;

      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
      manualReviewItems.push(...analysis.manualReviewItems);
    });

    // Calculate automated score
    const automatedScore = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      automatedScore,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
      manualReviewItems,
      automationRate: 70, // 70% automation as specified for Part 5
    };
  }

  /**
   * Analyze drag and drop elements
   */
  private async analyzeDragDropElements(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const dragDropElements = await page.$$eval(
        '[draggable="true"], .draggable, .ui-draggable',
        (elements) => {
          return elements.map((element, index) => {
            const rect = element.getBoundingClientRect();
            return {
              index,
              tagName: element.tagName.toLowerCase(),
              className: element.className,
              id: element.id,
              draggable: element.getAttribute('draggable'),
              hasKeyboardHandler:
                element.hasAttribute('onkeydown') || element.hasAttribute('onkeyup'),
              position: {
                x: rect.left,
                y: rect.top,
                width: rect.width,
                height: rect.height,
              },
              isVisible: rect.width > 0 && rect.height > 0,
            };
          });
        },
      );

      const totalChecks = dragDropElements.length;
      let passedChecks = 0;

      if (dragDropElements.length === 0) {
        evidence.push({
          type: 'text',
          description: 'No drag and drop elements found',
          value: 'Drag and drop elements found: false',
          element: 'page',
        });
        return {
          totalChecks: 1,
          passedChecks: 1,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      dragDropElements.forEach((element, index) => {
        if (element.hasKeyboardHandler) {
          passedChecks++;
          evidence.push({
            type: 'interaction',
            description: `Drag element ${index + 1} has keyboard handlers`,
            value: `${element.tagName}${element.id ? '#' + element.id : ''} - hasKeyboardHandler: true, className: ${element.className}`,
            severity: 'info',
          });
        } else {
          issues.push(`Drag element ${index + 1} lacks keyboard alternative`);
          evidence.push({
            type: 'interaction',
            description: `Drag element ${index + 1} may lack keyboard alternative`,
            value: `${element.tagName}${element.id ? '#' + element.id : ''} - hasKeyboardHandler: false, className: ${element.className}`,
            severity: 'warning',
          });
        }

        // Add manual review for each drag element
        manualReviewItems.push({
          selector: `${element.tagName}${element.id ? '#' + element.id : ''}`,
          description: `Verify drag element ${index + 1} has functional keyboard/click alternative`,
          automatedFindings: `Drag element detected with hasKeyboardHandler: ${element.hasKeyboardHandler}, className: ${element.className}`,
          reviewRequired: `Manual verification that drag functionality has keyboard or click alternative`,
          priority: 'high',
          estimatedTime: 4,
        });
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };
    } catch (error) {
      evidence.push({
        type: 'code',
        description: 'Error analyzing drag and drop elements',
        value: `[draggable] - error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze drag and drop elements'],
        recommendations: ['Check drag and drop functionality manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze sliders and range inputs
   */
  private async analyzeSliders(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const sliders = await page.$$eval('input[type="range"], .slider, .ui-slider', (elements) => {
        return elements.map((element, index) => {
          const input = element as HTMLInputElement;
          return {
            index,
            type: input.type || 'slider',
            min: input.min || '',
            max: input.max || '',
            step: input.step || '',
            value: input.value || '',
            hasLabel:
              !!element.getAttribute('aria-label') ||
              !!document.querySelector(`label[for="${element.id}"]`),
            hasKeyboardSupport: input.type === 'range', // Native range inputs have keyboard support
            className: element.className,
          };
        });
      });

      const totalChecks = sliders.length;
      let passedChecks = 0;

      if (sliders.length === 0) {
        evidence.push({
          type: 'text',
          description: 'No sliders found',
          value: 'page - slidersFound: false',
          severity: 'info',
        });
        return {
          totalChecks: 1,
          passedChecks: 1,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      sliders.forEach((slider, index) => {
        if (slider.type === 'range' || slider.hasKeyboardSupport) {
          passedChecks++;
          evidence.push({
            type: 'interaction',
            description: `Slider ${index + 1} has keyboard support`,
            value: `input[type="range"], .slider - type: ${slider.type}, hasKeyboardSupport: true, hasLabel: ${slider.hasLabel}`,
            severity: 'info',
          });
        } else {
          issues.push(`Slider ${index + 1} may lack keyboard support`);
          evidence.push({
            type: 'warning',
            description: `Slider ${index + 1} requires keyboard support verification`,
            value: `Type: ${slider.type}, Class: ${slider.className}`,
            element: `.slider, .ui-slider`,
          });
        }

        // Add manual review for custom sliders
        if (slider.type !== 'range') {
          manualReviewItems.push({
            selector: `.slider, .ui-slider`,
            description: `Test custom slider ${index + 1} keyboard functionality (arrow keys, page up/down)`,
            automatedFindings: `Slider ${index + 1}: Type: ${slider.type}, Class: ${slider.className}`,
            reviewRequired:
              'Test that slider can be operated using arrow keys, page up/down, and other keyboard controls',
            priority: 'high',
            estimatedTime: 3,
            type: 'slider_keyboard_support',
            element: `.slider, .ui-slider`,
            context: `Slider ${index + 1}: Type: ${slider.type}, Class: ${slider.className}`,
          });
        }
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };
    } catch (error) {
      evidence.push({
        type: 'error',
        description: 'Error analyzing sliders',
        value: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        element: 'input[type="range"], .slider',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze sliders'],
        recommendations: ['Check slider functionality manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze sortable lists
   */
  private async analyzeSortableLists(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const sortableLists = await page.$$eval(
        '.sortable, .ui-sortable, [data-sortable]',
        (elements) => {
          return elements.map((element, index) => ({
            index,
            tagName: element.tagName.toLowerCase(),
            className: element.className,
            itemCount: element.children.length,
            hasAriaSort: element.hasAttribute('aria-sort'),
            hasKeyboardHandlers:
              element.hasAttribute('onkeydown') || element.hasAttribute('onkeyup'),
          }));
        },
      );

      const totalChecks = sortableLists.length;
      const passedChecks = 0;

      if (sortableLists.length === 0) {
        evidence.push({
          type: 'info',
          description: 'No sortable lists found',
          value: 'Sortable lists found: false',
          element: 'page',
        });
        return {
          totalChecks: 1,
          passedChecks: 1,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      sortableLists.forEach((list, index) => {
        // All sortable lists require manual review for alternatives
        manualReviewItems.push({
          selector: `.sortable, .ui-sortable`,
          description: `Verify sortable list ${index + 1} has keyboard/button alternative for reordering`,
          automatedFindings: `List ${index + 1}: Items: ${list.itemCount}, ARIA sort: ${list.hasAriaSort}, Keyboard handlers: ${list.hasKeyboardHandlers}`,
          reviewRequired:
            'Test that list items can be reordered using keyboard controls or alternative buttons',
          priority: 'high',
          estimatedTime: 5,
          type: 'sortable_alternative',
          element: `.sortable, .ui-sortable`,
          context: `List ${index + 1}: Items: ${list.itemCount}, ARIA sort: ${list.hasAriaSort}, Keyboard handlers: ${list.hasKeyboardHandlers}`,
        });

        evidence.push({
          type: 'warning',
          description: `Sortable list ${index + 1} requires manual verification of alternatives`,
          value: `Items: ${list.itemCount}, ARIA sort: ${list.hasAriaSort}, Manual check required: true`,
          element: `.sortable, .ui-sortable`,
        });
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };
    } catch (error) {
      evidence.push({
        type: 'error',
        description: 'Error analyzing sortable lists',
        value: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        element: '.sortable, .ui-sortable',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze sortable lists'],
        recommendations: ['Check sortable functionality manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze resizable elements
   */
  private async analyzeResizableElements(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const resizableElements = await page.$$eval(
        '.resizable, .ui-resizable, [data-resizable]',
        (elements) => {
          return elements.map((element, index) => ({
            index,
            tagName: element.tagName.toLowerCase(),
            className: element.className,
            hasResizeHandles:
              element.querySelector('.ui-resizable-handle, .resize-handle') !== null,
          }));
        },
      );

      const totalChecks = resizableElements.length;
      const passedChecks = 0;

      if (resizableElements.length === 0) {
        evidence.push({
          type: 'info',
          description: 'No resizable elements found',
          value: 'Resizable elements found: false',
          element: 'page',
        });
        return {
          totalChecks: 1,
          passedChecks: 1,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      resizableElements.forEach((element, index) => {
        // All resizable elements require manual review
        manualReviewItems.push({
          selector: `.resizable, .ui-resizable`,
          description: `Verify resizable element ${index + 1} has keyboard/input alternative for resizing`,
          automatedFindings: `Element ${index + 1}: Has resize handles: ${element.hasResizeHandles}, Class: ${element.className}`,
          reviewRequired:
            'Test that element can be resized using keyboard controls or input fields',
          priority: 'medium',
          estimatedTime: 4,
          type: 'resizable_alternative',
          element: `.resizable, .ui-resizable`,
          context: `Element ${index + 1}: Has resize handles: ${element.hasResizeHandles}, Class: ${element.className}`,
        });

        evidence.push({
          type: 'warning',
          description: `Resizable element ${index + 1} requires manual verification of alternatives`,
          value: `Has resize handles: ${element.hasResizeHandles}, Manual check required: true`,
          element: `.resizable, .ui-resizable`,
        });
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };
    } catch (error) {
      evidence.push({
        type: 'error',
        description: 'Error analyzing resizable elements',
        value: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        element: '.resizable, .ui-resizable',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze resizable elements'],
        recommendations: ['Check resizable functionality manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze gesture interactions
   */
  private async analyzeGestureInteractions(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const gestureElements = await page.evaluate(() => {
        const elements = document.querySelectorAll('*');
        const gestureElements: any[] = [];

        elements.forEach((element, index) => {
          const hasTouch =
            element.hasAttribute('ontouchstart') ||
            element.hasAttribute('ontouchmove') ||
            element.hasAttribute('ontouchend');

          const hasSwipe =
            element.className.includes('swipe') || element.hasAttribute('data-swipe');

          if (hasTouch || hasSwipe) {
            gestureElements.push({
              index,
              tagName: element.tagName.toLowerCase(),
              className: element.className,
              hasTouch,
              hasSwipe,
              hasClick: element.hasAttribute('onclick') || element.hasAttribute('onmousedown'),
            });
          }
        });

        return gestureElements;
      });

      const totalChecks = gestureElements.length;
      let passedChecks = 0;

      if (gestureElements.length === 0) {
        evidence.push({
          type: 'info',
          description: 'No gesture interactions detected',
          value: 'Gesture elements found: false',
          element: 'page',
        });
        return {
          totalChecks: 1,
          passedChecks: 1,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      gestureElements.forEach((element, index) => {
        if (element.hasClick) {
          passedChecks++;
          evidence.push({
            type: 'info',
            description: `Gesture element ${index + 1} has click alternative`,
            value: `Touch: ${element.hasTouch}, Swipe: ${element.hasSwipe}, Click: ${element.hasClick}`,
            element: element.tagName,
          });
        } else {
          issues.push(`Gesture element ${index + 1} may lack click alternative`);
          evidence.push({
            type: 'warning',
            description: `Gesture element ${index + 1} may need click alternative`,
            value: `Touch: ${element.hasTouch}, Swipe: ${element.hasSwipe}, Click: ${element.hasClick}`,
            element: element.tagName,
          });
        }

        // Add manual review for all gesture interactions
        manualReviewItems.push({
          selector: element.tagName,
          description: `Test gesture element ${index + 1} for non-dragging alternatives`,
          automatedFindings: `Element has touch: ${element.hasTouch}, swipe: ${element.hasSwipe}, click: ${element.hasClick}`,
          reviewRequired: 'Verify non-dragging alternatives are available and functional',
          priority: 'high',
          estimatedTime: 3,
          type: 'gesture_alternative',
          element: element.tagName,
          context: `Element ${index + 1}: Type: ${element.hasSwipe ? 'swipe' : 'touch'}, Click alternative: ${element.hasClick}`,
        });
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };
    } catch (error) {
      evidence.push({
        type: 'error',
        description: 'Error analyzing gesture interactions',
        value: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        element: 'gesture elements',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze gesture interactions'],
        recommendations: ['Check gesture functionality manually'],
        manualReviewItems,
      };
    }
  }
}
