/**
 * WCAG Rule 17: Image Alternatives 3.0 - Enhanced image alternative analysis
 * 95% Automated - Minimal manual review for complex image content
 */

import { Page } from 'puppeteer';
import {
  ManualReviewConfig,
  ManualReviewTemplate,
  ManualReviewItem,
} from '../utils/manual-review-template';
import { WcagEvidence } from '../types';

export class ImageAlternatives3Check {
  private checkTemplate = new ManualReviewTemplate();

  /**
   * Perform enhanced image alternatives check - 95% automated
   */
  async performCheck(config: ManualReviewConfig) {
    return this.checkTemplate.executeManualReviewCheck(
      'WCAG-017',
      'Image Alternatives 3.0',
      'perceivable',
      0.06,
      'AAA',
      0.95, // 95% automation rate
      config,
      this.executeImageAlternatives3Check.bind(this),
    );
  }

  /**
   * Execute enhanced image alternatives analysis
   */
  private async executeImageAlternatives3Check(page: Page, _config: ManualReviewConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    // Enhanced image analysis
    const imageAnalysis = await this.analyzeEnhancedImages(page);

    // Complex image content analysis
    const complexImageAnalysis = await this.analyzeComplexImages(page);

    // Image context analysis
    const contextAnalysis = await this.analyzeImageContext(page);

    // Combine all analyses
    const allAnalyses = [imageAnalysis, complexImageAnalysis, contextAnalysis];

    let totalChecks = 0;
    let passedChecks = 0;
    let manualReviewCount = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      manualReviewCount += analysis.manualReviewItems.length;

      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
      manualReviewItems.push(...analysis.manualReviewItems);
    });

    // Calculate automated score
    const automatedChecks = totalChecks - manualReviewCount;
    const automatedScore =
      automatedChecks > 0 ? Math.round((passedChecks / automatedChecks) * 100) : 100;

    // Add summary evidence
    evidence.unshift({
      type: 'text',
      description: 'Enhanced image alternatives analysis summary',
      value: `${passedChecks}/${automatedChecks} checks pass automated tests, ${manualReviewCount} require manual review`,
      severity: automatedScore >= 90 ? 'info' : automatedScore >= 70 ? 'warning' : 'error',
    });

    return {
      automatedScore,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
      manualReviewItems,
      automationRate: 0.95,
    };
  }

  /**
   * Enhanced image analysis with advanced pattern detection
   */
  private async analyzeEnhancedImages(page: Page) {
    return await page.evaluate(() => {
      const evidence: any[] = [];
      const issues: string[] = [];
      const recommendations: string[] = [];
      const manualReviewItems: any[] = [];

      function generateSelector(element: Element, index: number): string {
        if (element.id) {
          return `#${element.id}`;
        }

        if (element.className) {
          const classes = element.className
            .toString()
            .split(' ')
            .filter((c) => c.length > 0);
          if (classes.length > 0) {
            return `${element.tagName.toLowerCase()}.${classes.join('.')}`;
          }
        }

        return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
      }

      function analyzeImageContent(img: HTMLImageElement): {
        isComplex: boolean;
        isInformative: boolean;
        isDecorative: boolean;
        contentType: string;
        reason: string;
      } {
        const src = img.src.toLowerCase();
        const alt = img.alt?.toLowerCase() || '';
        const className = img.className.toLowerCase();

        // Check for complex images (charts, graphs, diagrams)
        const complexPatterns = [
          /chart/i,
          /graph/i,
          /diagram/i,
          /infographic/i,
          /flowchart/i,
          /timeline/i,
          /map/i,
          /plot/i,
          /visualization/i,
          /dashboard/i,
        ];

        const isComplex = complexPatterns.some(
          (pattern) => pattern.test(src) || pattern.test(alt) || pattern.test(className),
        );

        // Check for decorative patterns
        const decorativePatterns = [
          /decoration/i,
          /ornament/i,
          /border/i,
          /spacer/i,
          /divider/i,
          /separator/i,
          /background/i,
          /texture/i,
          /pattern/i,
          /hero/i,
          /banner/i,
        ];

        const isDecorative =
          decorativePatterns.some((pattern) => pattern.test(src) || pattern.test(className)) ||
          img.getAttribute('role') === 'presentation' ||
          img.getAttribute('aria-hidden') === 'true';

        // Check for informative content
        const informativePatterns = [
          /product/i,
          /screenshot/i,
          /tutorial/i,
          /instruction/i,
          /example/i,
          /before/i,
          /after/i,
          /result/i,
          /comparison/i,
        ];

        const isInformative = informativePatterns.some(
          (pattern) => pattern.test(src) || pattern.test(alt) || pattern.test(className),
        );

        let contentType = 'unknown';
        let reason = '';

        if (isComplex) {
          contentType = 'complex';
          reason = 'Image appears to contain complex information (chart, graph, or diagram)';
        } else if (isDecorative) {
          contentType = 'decorative';
          reason = 'Image appears to be decorative';
        } else if (isInformative) {
          contentType = 'informative';
          reason = 'Image appears to contain informative content';
        } else {
          contentType = 'simple';
          reason = 'Image appears to be simple informative content';
        }

        return { isComplex, isInformative, isDecorative, contentType, reason };
      }

      function validateAltTextQuality(
        img: HTMLImageElement,
        contentAnalysis: any,
      ): {
        score: number;
        issues: string[];
        recommendations: string[];
      } {
        const alt = img.alt || '';
        const issues: string[] = [];
        const recommendations: string[] = [];
        let score = 0;

        // Basic presence check
        if (!alt && !contentAnalysis.isDecorative) {
          issues.push('Missing alt text');
          recommendations.push('Add descriptive alt text');
          return { score: 0, issues, recommendations };
        }

        if (contentAnalysis.isDecorative) {
          if (alt === '') {
            score = 100; // Perfect for decorative images
          } else {
            issues.push('Decorative image should have empty alt text');
            recommendations.push('Use alt="" for decorative images');
            score = 50;
          }
          return { score, issues, recommendations };
        }

        // Quality checks for non-decorative images
        if (alt.length < 3) {
          issues.push('Alt text too short');
          recommendations.push('Provide more descriptive alt text');
          score = 20;
        } else if (alt.length > 125) {
          issues.push('Alt text very long (consider using longdesc or aria-describedby)');
          recommendations.push('Consider shorter alt text with additional description');
          score = 70;
        } else {
          score = 80; // Good length
        }

        // Check for redundant phrases
        const redundantPhrases = [
          /image of/i,
          /picture of/i,
          /photo of/i,
          /graphic of/i,
          /icon of/i,
          /click here/i,
          /link to/i,
          /button/i,
        ];

        if (redundantPhrases.some((phrase) => phrase.test(alt))) {
          issues.push('Alt text contains redundant phrases');
          recommendations.push('Remove redundant phrases like "image of" from alt text');
          score -= 20;
        }

        // Check for filename patterns
        const filenamePatterns = [
          /\.(jpg|jpeg|png|gif|svg|webp)$/i,
          /^img\d+/i,
          /^dsc\d+/i,
          /^untitled/i,
          /^[a-f0-9]{8,}$/i, // Hash-like strings
        ];

        if (filenamePatterns.some((pattern) => pattern.test(alt))) {
          issues.push('Alt text appears to be filename');
          recommendations.push('Replace filename with descriptive text');
          score = 10;
        }

        return { score: Math.max(0, score), issues, recommendations };
      }

      const images = Array.from(document.querySelectorAll('img'));
      let totalChecks = 0;
      let passedChecks = 0;

      if (images.length === 0) {
        return {
          totalChecks: 0,
          passedChecks: 0,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      images.forEach((img, index) => {
        const selector = generateSelector(img, index);
        const contentAnalysis = analyzeImageContent(img);
        const altQuality = validateAltTextQuality(img, contentAnalysis);

        totalChecks++;

        if (altQuality.score >= 80) {
          passedChecks++;
          evidence.push({
            type: 'text',
            description: 'Image has high-quality alternative text',
            value: `${contentAnalysis.contentType} image with score: ${altQuality.score}/100`,
            selector,
            severity: 'info',
          });
        } else if (altQuality.score >= 50) {
          evidence.push({
            type: 'text',
            description: 'Image has adequate alternative text',
            value: `${contentAnalysis.contentType} image with score: ${altQuality.score}/100`,
            selector,
            severity: 'warning',
          });
          passedChecks++; // Still passing but with warnings
        } else {
          issues.push(`Poor quality alt text: ${selector}`);
          altQuality.issues.forEach((issue) => issues.push(`${selector}: ${issue}`));
          altQuality.recommendations.forEach((rec) => recommendations.push(`${selector}: ${rec}`));
        }

        // Complex images need manual review regardless of alt text quality
        if (contentAnalysis.isComplex) {
          manualReviewItems.push({
            selector,
            description: 'Complex image content verification needed',
            automatedFindings: `${contentAnalysis.reason}. Alt text: "${img.alt || 'none'}"`,
            reviewRequired:
              'Verify that complex information is adequately described or provide long description',
            priority: 'high',
            estimatedTime: 5,
          });
        }
      });

      return { totalChecks, passedChecks, evidence, issues, recommendations, manualReviewItems };
    });
  }

  /**
   * Analyze complex images requiring detailed descriptions
   */
  private async analyzeComplexImages(page: Page) {
    return await page.evaluate(() => {
      const evidence: any[] = [];
      const issues: string[] = [];
      const recommendations: string[] = [];
      const manualReviewItems: any[] = [];

      // Look for images that might need long descriptions
      const complexImages = Array.from(document.querySelectorAll('img')).filter((img) => {
        const src = img.src.toLowerCase();
        const alt = img.alt?.toLowerCase() || '';
        const className = img.className.toLowerCase();

        const complexIndicators = [
          /chart/i,
          /graph/i,
          /diagram/i,
          /infographic/i,
          /flowchart/i,
          /map/i,
          /screenshot/i,
          /dashboard/i,
          /visualization/i,
        ];

        return complexIndicators.some(
          (indicator) => indicator.test(src) || indicator.test(alt) || indicator.test(className),
        );
      });

      const totalChecks = complexImages.length;
      let passedChecks = 0;

      if (complexImages.length === 0) {
        return {
          totalChecks: 0,
          passedChecks: 0,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      complexImages.forEach((img, index) => {
        const selector = img.id ? `#${img.id}` : `img:nth-of-type(${index + 1})`;

        // Check for long description mechanisms
        const hasLongDesc =
          img.hasAttribute('longdesc') ||
          img.getAttribute('aria-describedby') ||
          img.closest('figure')?.querySelector('figcaption') ||
          (img.nextElementSibling?.textContent?.length || 0) > 100;

        if (hasLongDesc) {
          passedChecks++;
          evidence.push({
            type: 'text',
            description: 'Complex image has extended description mechanism',
            value: 'Long description or detailed caption available',
            selector,
            severity: 'info',
          });
        } else {
          manualReviewItems.push({
            selector,
            description: 'Complex image may need extended description',
            automatedFindings: 'Complex image without apparent long description mechanism',
            reviewRequired: 'Determine if image needs extended description beyond alt text',
            priority: 'high',
            estimatedTime: 7,
          });
        }
      });

      return { totalChecks, passedChecks, evidence, issues, recommendations, manualReviewItems };
    });
  }

  /**
   * Analyze image context and surrounding content
   */
  private async analyzeImageContext(page: Page) {
    return await page.evaluate(() => {
      const evidence: any[] = [];
      const issues: string[] = [];
      const recommendations: string[] = [];
      const manualReviewItems: any[] = [];

      const images = Array.from(document.querySelectorAll('img'));
      let totalChecks = 0;
      let passedChecks = 0;

      if (images.length === 0) {
        return {
          totalChecks: 0,
          passedChecks: 0,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      images.forEach((img, index) => {
        const selector = img.id ? `#${img.id}` : `img:nth-of-type(${index + 1})`;

        totalChecks++;

        // Analyze surrounding context
        const parent = img.parentElement;
        const figure = img.closest('figure');
        const link = img.closest('a');

        let contextScore = 0;
        const contextFeatures: string[] = [];

        // Check if in figure with caption
        if (figure) {
          const caption = figure.querySelector('figcaption');
          if (caption && caption.textContent?.trim()) {
            contextScore += 30;
            contextFeatures.push('has figure caption');
          }
        }

        // Check if in link
        if (link) {
          const linkText = link.textContent?.trim();
          const linkTitle = link.getAttribute('title');
          if (linkText || linkTitle) {
            contextScore += 20;
            contextFeatures.push('in descriptive link');
          }
        }

        // Check surrounding text
        const surroundingText = parent?.textContent?.replace(img.alt || '', '').trim() || '';
        if (surroundingText.length > 50) {
          contextScore += 25;
          contextFeatures.push('has surrounding text');
        }

        // Check for aria-describedby
        if (img.getAttribute('aria-describedby')) {
          contextScore += 25;
          contextFeatures.push('has aria-describedby');
        }

        if (contextScore >= 50 || contextFeatures.length >= 2) {
          passedChecks++;
          evidence.push({
            type: 'text',
            description: 'Image has good contextual support',
            value: `Context features: ${contextFeatures.join(', ')}`,
            selector,
            severity: 'info',
          });
        } else if (contextScore >= 20) {
          passedChecks++;
          evidence.push({
            type: 'text',
            description: 'Image has some contextual support',
            value: `Context features: ${contextFeatures.join(', ') || 'minimal context'}`,
            selector,
            severity: 'warning',
          });
        } else {
          issues.push(`Image lacks contextual support: ${selector}`);
          recommendations.push(
            `Consider adding caption, surrounding text, or better context for ${selector}`,
          );
        }
      });

      return { totalChecks, passedChecks, evidence, issues, recommendations, manualReviewItems };
    });
  }
}
