import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { URL } from 'url';
import { JSDOM } from 'jsdom';

export interface UrlResolutionOptions {
  timeout?: number;
  maxRedirects?: number;
  userAgent?: string;
  retryAttempts?: number;
  retryDelay?: number;
  followRedirects?: boolean;
  validateSsl?: boolean;
}

export interface ResolvedUrl {
  originalUrl: string;
  finalUrl: string;
  redirectChain: string[];
  statusCode: number;
  contentType: string;
  responseTime: number;
  isAccessible: boolean;
  error?: string;
  metadata: {
    title?: string;
    language?: string;
    charset?: string;
    lastModified?: string;
  };
}

export interface ContentDiscoveryResult {
  privacyPolicyLinks: Array<{
    url: string;
    text: string;
    confidence: number;
    location: string;
  }>;
  cookieBannerElements: Array<{
    selector: string;
    text: string;
    type: 'banner' | 'modal' | 'popup';
  }>;
  contactLinks: Array<{
    url: string;
    text: string;
    type: 'email' | 'form' | 'phone';
  }>;
  legalPages: Array<{
    url: string;
    text: string;
    type: 'terms' | 'imprint' | 'legal';
  }>;
}

/**
 * Smart URL Resolver for GDPR compliance scanning
 * Handles complex URL resolution, redirects, and content discovery
 */
export class SmartUrlResolver {
  private httpClient: AxiosInstance;
  private static readonly USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'GDPR-Compliance-Scanner/2.0 (+https://comply-checker.com/bot)',
  ];

  private static readonly PRIVACY_POLICY_PATTERNS = [
    // English patterns
    /privacy\s*policy/i,
    /privacy\s*notice/i,
    /privacy\s*statement/i,
    /data\s*protection/i,
    /data\s*privacy/i,

    // German patterns
    /datenschutz/i,
    /datenschutzerklärung/i,
    /datenschutzrichtlinie/i,

    // French patterns
    /politique\s*de\s*confidentialité/i,
    /protection\s*des\s*données/i,

    // Spanish patterns
    /política\s*de\s*privacidad/i,
    /protección\s*de\s*datos/i,

    // Italian patterns
    /informativa\s*privacy/i,
    /protezione\s*dati/i,
  ];

  private static readonly COOKIE_BANNER_SELECTORS = [
    // Common cookie banner selectors
    '[id*="cookie"]',
    '[class*="cookie"]',
    '[id*="consent"]',
    '[class*="consent"]',
    '[id*="gdpr"]',
    '[class*="gdpr"]',
    '[data-testid*="cookie"]',
    '[data-cy*="cookie"]',

    // Popular cookie management platforms
    '#onetrust-banner-sdk',
    '.ot-sdk-container',
    '#cookieChoiceInfo',
    '.cc-banner',
    '.cookie-banner',
    '.consent-banner',
    '.gdpr-banner',
    '.privacy-banner',

    // Modal and popup selectors
    '[role="dialog"][aria-label*="cookie" i]',
    '[role="dialog"][aria-label*="consent" i]',
    '.modal[class*="cookie"]',
    '.popup[class*="cookie"]',
  ];

  constructor(options: UrlResolutionOptions = {}) {
    this.httpClient = axios.create({
      timeout: options.timeout || 30000,
      maxRedirects: options.maxRedirects || 5,
      validateStatus: (status) => status < 500, // Accept 4xx as valid responses
      headers: {
        'User-Agent': options.userAgent || SmartUrlResolver.USER_AGENTS[0],
        Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9,de;q=0.8,fr;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        DNT: '1',
        Connection: 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      },
    });

    // Add request interceptor for security validation
    this.httpClient.interceptors.request.use((config) => {
      if (config.url && !this.isAllowedUrl(config.url)) {
        throw new Error('URL not allowed for security reasons');
      }
      return config;
    });
  }

  /**
   * Resolve a URL with comprehensive analysis
   */
  async resolveUrl(url: string, options: UrlResolutionOptions = {}): Promise<ResolvedUrl> {
    const startTime = Date.now();
    const redirectChain: string[] = [url];
    let currentUrl = url;
    let finalResponse: AxiosResponse | null = null;
    let error: string | undefined;

    try {
      // Normalize and validate URL
      currentUrl = this.normalizeUrl(url);

      // Attempt resolution with retry logic
      finalResponse = await this.attemptUrlResolution(currentUrl, options, redirectChain);

      // Extract metadata from response
      const metadata = await this.extractMetadata(finalResponse);

      return {
        originalUrl: url,
        finalUrl: currentUrl,
        redirectChain,
        statusCode: finalResponse.status,
        contentType: finalResponse.headers['content-type'] || 'unknown',
        responseTime: Date.now() - startTime,
        isAccessible: finalResponse.status < 400,
        metadata,
      };
    } catch (err) {
      error = err instanceof Error ? err.message : 'Unknown error';

      return {
        originalUrl: url,
        finalUrl: currentUrl,
        redirectChain,
        statusCode: 0,
        contentType: 'unknown',
        responseTime: Date.now() - startTime,
        isAccessible: false,
        error,
        metadata: {},
      };
    }
  }

  /**
   * Discover content elements on a webpage
   */
  async discoverContent(url: string, html?: string): Promise<ContentDiscoveryResult> {
    let content = html;

    if (!content) {
      try {
        const resolved = await this.resolveUrl(url);
        if (resolved.isAccessible) {
          const response = await this.httpClient.get(resolved.finalUrl);
          content = response.data;
        } else {
          throw new Error('URL not accessible');
        }
      } catch (error) {
        console.warn(`Failed to fetch content for discovery: ${error}`);
        return this.createEmptyDiscoveryResult();
      }
    }

    try {
      const dom = new JSDOM(content);
      const document = dom.window.document;
      const baseUrl = url;

      return {
        privacyPolicyLinks: this.findPrivacyPolicyLinks(document, baseUrl),
        cookieBannerElements: this.findCookieBannerElements(document),
        contactLinks: this.findContactLinks(document, baseUrl),
        legalPages: this.findLegalPages(document, baseUrl),
      };
    } catch (error) {
      console.warn(`Content discovery failed: ${error}`);
      return this.createEmptyDiscoveryResult();
    }
  }

  /**
   * Normalize URL to standard format
   */
  private normalizeUrl(url: string): string {
    try {
      // Add protocol if missing
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = 'https://' + url;
      }

      const urlObj = new URL(url);

      // Remove common tracking parameters
      const trackingParams = [
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'utm_content',
        'utm_term',
        'fbclid',
        'gclid',
      ];
      trackingParams.forEach((param) => urlObj.searchParams.delete(param));

      // Normalize path
      urlObj.pathname = urlObj.pathname.replace(/\/+/g, '/');
      if (urlObj.pathname.endsWith('/') && urlObj.pathname.length > 1) {
        urlObj.pathname = urlObj.pathname.slice(0, -1);
      }

      return urlObj.toString();
    } catch (error) {
      throw new Error(`Invalid URL format: ${url}`);
    }
  }

  /**
   * Attempt URL resolution with retry logic and user agent rotation
   */
  private async attemptUrlResolution(
    url: string,
    options: UrlResolutionOptions,
    redirectChain: string[],
  ): Promise<AxiosResponse> {
    const maxAttempts = options.retryAttempts || 3;
    const retryDelay = options.retryDelay || 1000;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        // Rotate user agent on retry
        const userAgent =
          SmartUrlResolver.USER_AGENTS[(attempt - 1) % SmartUrlResolver.USER_AGENTS.length];

        const response = await this.httpClient.get(url, {
          headers: {
            'User-Agent': userAgent,
          },
          timeout: options.timeout || 30000,
        });

        // Track redirects
        if (response.request.res?.responseUrl && response.request.res.responseUrl !== url) {
          redirectChain.push(response.request.res.responseUrl);
        }

        return response;
      } catch (error) {
        console.warn(
          `URL resolution attempt ${attempt}/${maxAttempts} failed for ${url}: ${error}`,
        );

        if (attempt === maxAttempts) {
          throw error;
        }

        // Wait before retry
        await new Promise((resolve) => setTimeout(resolve, retryDelay * attempt));
      }
    }

    throw new Error('All resolution attempts failed');
  }

  /**
   * Extract metadata from HTTP response
   */
  private async extractMetadata(response: AxiosResponse): Promise<ResolvedUrl['metadata']> {
    const metadata: ResolvedUrl['metadata'] = {};

    try {
      if (response.headers['content-type']?.includes('text/html')) {
        const dom = new JSDOM(response.data);
        const document = dom.window.document;

        // Extract title
        const titleElement = document.querySelector('title');
        if (titleElement) {
          metadata.title = titleElement.textContent?.trim();
        }

        // Extract language
        const htmlElement = document.querySelector('html');
        if (htmlElement) {
          metadata.language = htmlElement.getAttribute('lang') || undefined;
        }

        // Extract charset
        const charsetMeta = document.querySelector('meta[charset]');
        if (charsetMeta) {
          metadata.charset = charsetMeta.getAttribute('charset') || undefined;
        }
      }

      // Extract last modified from headers
      if (response.headers['last-modified']) {
        metadata.lastModified = response.headers['last-modified'];
      }
    } catch (error) {
      console.warn('Failed to extract metadata:', error);
    }

    return metadata;
  }

  /**
   * Find privacy policy links on a webpage
   */
  private findPrivacyPolicyLinks(
    document: Document,
    baseUrl: string,
  ): ContentDiscoveryResult['privacyPolicyLinks'] {
    const links: ContentDiscoveryResult['privacyPolicyLinks'] = [];
    const allLinks = Array.from(document.querySelectorAll('a[href]'));

    for (const link of allLinks) {
      const href = link.getAttribute('href');
      const text = link.textContent?.trim() || '';

      if (!href || !text) continue;

      // Check if link text matches privacy policy patterns
      const confidence = this.calculatePrivacyPolicyConfidence(text, href);

      if (confidence > 0.3) {
        // Minimum confidence threshold
        const resolvedUrl = this.resolveRelativeUrl(href, baseUrl);
        if (resolvedUrl) {
          links.push({
            url: resolvedUrl,
            text,
            confidence,
            location: this.getElementLocation(link),
          });
        }
      }
    }

    // Sort by confidence (highest first)
    return links.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Find cookie banner elements
   */
  private findCookieBannerElements(
    document: Document,
  ): ContentDiscoveryResult['cookieBannerElements'] {
    const elements: ContentDiscoveryResult['cookieBannerElements'] = [];

    for (const selector of SmartUrlResolver.COOKIE_BANNER_SELECTORS) {
      try {
        const foundElements = document.querySelectorAll(selector);

        for (const element of foundElements) {
          const text = element.textContent?.trim() || '';
          if (text.length > 10) {
            // Filter out empty or very short elements
            elements.push({
              selector,
              text: text.substring(0, 200), // Limit text length
              type: this.determineBannerType(element),
            });
          }
        }
      } catch (error) {
        // Skip invalid selectors
        continue;
      }
    }

    return elements;
  }

  /**
   * Find contact links (email, forms, phone)
   */
  private findContactLinks(
    document: Document,
    baseUrl: string,
  ): ContentDiscoveryResult['contactLinks'] {
    const links: ContentDiscoveryResult['contactLinks'] = [];

    // Find email links
    const emailLinks = document.querySelectorAll('a[href^="mailto:"]');
    for (const link of emailLinks) {
      const href = link.getAttribute('href');
      const text = link.textContent?.trim() || '';
      if (href) {
        links.push({
          url: href,
          text,
          type: 'email',
        });
      }
    }

    // Find contact forms
    const contactForms = document.querySelectorAll('form');
    for (const form of contactForms) {
      const action = form.getAttribute('action');
      const formText = form.textContent?.toLowerCase() || '';

      if (
        formText.includes('contact') ||
        formText.includes('message') ||
        formText.includes('inquiry')
      ) {
        const resolvedUrl = action ? this.resolveRelativeUrl(action, baseUrl) : baseUrl;
        if (resolvedUrl) {
          links.push({
            url: resolvedUrl,
            text: 'Contact Form',
            type: 'form',
          });
        }
      }
    }

    // Find phone links
    const phoneLinks = document.querySelectorAll('a[href^="tel:"]');
    for (const link of phoneLinks) {
      const href = link.getAttribute('href');
      const text = link.textContent?.trim() || '';
      if (href) {
        links.push({
          url: href,
          text,
          type: 'phone',
        });
      }
    }

    return links;
  }

  /**
   * Find legal pages (terms, imprint, legal notice)
   */
  private findLegalPages(
    document: Document,
    baseUrl: string,
  ): ContentDiscoveryResult['legalPages'] {
    const pages: ContentDiscoveryResult['legalPages'] = [];
    const allLinks = Array.from(document.querySelectorAll('a[href]'));

    const legalPatterns = [
      {
        patterns: [
          /terms\s*of\s*(service|use)/i,
          /nutzungsbedingungen/i,
          /conditions\s*d'utilisation/i,
        ],
        type: 'terms' as const,
      },
      {
        patterns: [/imprint/i, /impressum/i, /mentions\s*légales/i, /aviso\s*legal/i],
        type: 'imprint' as const,
      },
      {
        patterns: [/legal\s*notice/i, /rechtliche\s*hinweise/i, /avis\s*juridique/i],
        type: 'legal' as const,
      },
    ];

    for (const link of allLinks) {
      const href = link.getAttribute('href');
      const text = link.textContent?.trim() || '';

      if (!href || !text) continue;

      for (const { patterns, type } of legalPatterns) {
        if (patterns.some((pattern) => pattern.test(text))) {
          const resolvedUrl = this.resolveRelativeUrl(href, baseUrl);
          if (resolvedUrl) {
            pages.push({
              url: resolvedUrl,
              text,
              type,
            });
          }
          break; // Only match first pattern type
        }
      }
    }

    return pages;
  }

  /**
   * Calculate confidence score for privacy policy links
   */
  private calculatePrivacyPolicyConfidence(text: string, href: string): number {
    let confidence = 0;
    const lowerText = text.toLowerCase();
    const lowerHref = href.toLowerCase();

    // Check text patterns
    for (const pattern of SmartUrlResolver.PRIVACY_POLICY_PATTERNS) {
      if (pattern.test(lowerText)) {
        confidence += 0.4;
        break;
      }
    }

    // Check href patterns
    if (lowerHref.includes('privacy') || lowerHref.includes('datenschutz')) {
      confidence += 0.3;
    }

    // Boost confidence for exact matches
    if (lowerText === 'privacy policy' || lowerText === 'datenschutz') {
      confidence += 0.3;
    }

    // Reduce confidence for generic terms
    if (lowerText.includes('more') || lowerText.includes('read') || lowerText.includes('learn')) {
      confidence -= 0.1;
    }

    return Math.min(1.0, Math.max(0, confidence));
  }

  /**
   * Determine the type of cookie banner element
   */
  private determineBannerType(element: Element): 'banner' | 'modal' | 'popup' {
    const classList = element.className.toLowerCase();
    const _id = element.id.toLowerCase();

    if (classList.includes('modal') || element.getAttribute('role') === 'dialog') {
      return 'modal';
    }

    if (classList.includes('popup') || classList.includes('overlay')) {
      return 'popup';
    }

    return 'banner';
  }

  /**
   * Get the location of an element on the page
   */
  private getElementLocation(element: Element): string {
    const parent = element.closest('header, footer, nav, main, aside');
    if (parent) {
      return parent.tagName.toLowerCase();
    }

    // Check if element is in the top or bottom portion of the page
    const rect = element.getBoundingClientRect?.();
    if (rect) {
      if (rect.top < window.innerHeight * 0.3) {
        return 'top';
      } else if (rect.top > window.innerHeight * 0.7) {
        return 'bottom';
      }
    }

    return 'middle';
  }

  /**
   * Resolve relative URL to absolute URL
   */
  private resolveRelativeUrl(href: string, baseUrl: string): string | null {
    try {
      // If href is already absolute, return it
      if (href.startsWith('http://') || href.startsWith('https://')) {
        return href;
      }

      // Skip non-HTTP protocols
      if (href.startsWith('mailto:') || href.startsWith('tel:') || href.startsWith('javascript:')) {
        return href;
      }

      // Resolve relative URL
      const base = new URL(baseUrl);
      const resolved = new URL(href, base);

      return resolved.toString();
    } catch (error) {
      console.warn('Failed to resolve relative URL:', href, error);
      return null;
    }
  }

  /**
   * Check if URL is allowed for security reasons
   */
  private isAllowedUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);

      // Block local/private networks
      const hostname = urlObj.hostname.toLowerCase();

      // Block localhost and local IPs
      if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname === '::1') {
        return false;
      }

      // Block private IP ranges
      const privateRanges = [
        /^10\./,
        /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
        /^192\.168\./,
        /^169\.254\./, // Link-local
        /^fc00:/, // IPv6 private
        /^fe80:/, // IPv6 link-local
      ];

      if (privateRanges.some((range) => range.test(hostname))) {
        return false;
      }

      // Only allow HTTP and HTTPS
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return false;
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Create empty discovery result
   */
  private createEmptyDiscoveryResult(): ContentDiscoveryResult {
    return {
      privacyPolicyLinks: [],
      cookieBannerElements: [],
      contactLinks: [],
      legalPages: [],
    };
  }

  /**
   * Batch resolve multiple URLs efficiently
   */
  async batchResolveUrls(
    urls: string[],
    options: UrlResolutionOptions = {},
  ): Promise<ResolvedUrl[]> {
    const batchSize = 5; // Process 5 URLs at a time to avoid overwhelming the target
    const results: ResolvedUrl[] = [];

    for (let i = 0; i < urls.length; i += batchSize) {
      const batch = urls.slice(i, i + batchSize);
      const batchPromises = batch.map((url) => this.resolveUrl(url, options));

      try {
        const batchResults = await Promise.allSettled(batchPromises);

        for (const result of batchResults) {
          if (result.status === 'fulfilled') {
            results.push(result.value);
          } else {
            // Create failed result for rejected promises
            const failedUrl = batch[batchResults.indexOf(result)];
            results.push({
              originalUrl: failedUrl,
              finalUrl: failedUrl,
              redirectChain: [failedUrl],
              statusCode: 0,
              contentType: 'unknown',
              responseTime: 0,
              isAccessible: false,
              error: result.reason?.message || 'Unknown error',
              metadata: {},
            });
          }
        }
      } catch (error) {
        console.error('Batch URL resolution failed:', error);
      }

      // Small delay between batches to be respectful
      if (i + batchSize < urls.length) {
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
    }

    return results;
  }

  /**
   * Test URL accessibility with minimal overhead
   */
  async testUrlAccessibility(url: string, timeout: number = 10000): Promise<boolean> {
    try {
      const response = await this.httpClient.head(url, { timeout });
      return response.status < 400;
    } catch (error) {
      return false;
    }
  }
}
