/**
 * WCAG Rule 6: Focus Order - 2.4.3
 * 75% Automated - Manual review for complex layouts and logical flow
 */

import { Page } from 'puppeteer';
import {
  ManualReviewConfig,
  ManualReviewTemplate,
  ManualReviewItem,
} from '../utils/manual-review-template';
import { WcagEvidence } from '../types';

interface FocusableElement {
  selector: string;
  tagName: string;
  type?: string;
  tabIndex: number;
  position: { x: number; y: number; width: number; height: number };
  isVisible: boolean;
  ariaLabel?: string;
  text?: string;
}

export class FocusOrderCheck {
  private checkTemplate = new ManualReviewTemplate();

  /**
   * Perform focus order check - 75% automated
   */
  async performCheck(config: ManualReviewConfig) {
    return this.checkTemplate.executeManualReviewCheck(
      'WCAG-006',
      'Focus Order',
      'operable',
      0.09,
      'A',
      0.75, // 75% automation rate
      config,
      this.executeFocusOrderCheck.bind(this),
    );
  }

  /**
   * Execute comprehensive focus order analysis
   */
  private async executeFocusOrderCheck(page: Page, _config: ManualReviewConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    // Get all focusable elements
    const focusableElements = await this.getFocusableElements(page);

    // Analyze tab order
    const tabOrderAnalysis = await this.analyzeTabOrder(page, focusableElements);

    // Analyze visual layout correlation
    const layoutAnalysis = await this.analyzeLayoutCorrelation(focusableElements);

    // Check for skip links
    const skipLinkAnalysis = await this.analyzeSkipLinks(page);

    // Combine all analyses
    const allAnalyses = [tabOrderAnalysis, layoutAnalysis, skipLinkAnalysis];

    let totalChecks = 0;
    let passedChecks = 0;
    let _manualReviewCount = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      _manualReviewCount += analysis.manualReviewItems.length;

      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
      manualReviewItems.push(...analysis.manualReviewItems);
    });

    // Calculate automated score
    const automatedScore = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      automatedScore,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
      manualReviewItems,
      automationRate: 75, // 75% automation as specified for Part 5
    };
  }

  /**
   * Get all focusable elements on the page
   */
  private async getFocusableElements(page: Page): Promise<FocusableElement[]> {
    return await page.evaluate(() => {
      const focusableSelectors = [
        'a[href]',
        'button:not([disabled])',
        'input:not([disabled]):not([type="hidden"])',
        'select:not([disabled])',
        'textarea:not([disabled])',
        '[tabindex]:not([tabindex="-1"])',
        'iframe',
        'object',
        'embed',
        'area[href]',
        'audio[controls]',
        'video[controls]',
        '[contenteditable="true"]',
      ];

      const elements: FocusableElement[] = [];

      focusableSelectors.forEach((selector) => {
        const nodeList = document.querySelectorAll(selector);
        nodeList.forEach((element, index) => {
          const rect = element.getBoundingClientRect();
          const computedStyle = window.getComputedStyle(element);

          const isVisible =
            rect.width > 0 &&
            rect.height > 0 &&
            computedStyle.visibility !== 'hidden' &&
            computedStyle.display !== 'none';

          if (isVisible) {
            elements.push({
              selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
              tagName: element.tagName.toLowerCase(),
              type: (element as HTMLInputElement).type || undefined,
              tabIndex: (element as HTMLElement).tabIndex,
              position: {
                x: rect.left,
                y: rect.top,
                width: rect.width,
                height: rect.height,
              },
              isVisible,
              ariaLabel: element.getAttribute('aria-label') || undefined,
              text: element.textContent?.trim().substring(0, 50) || undefined,
            });
          }
        });
      });

      return elements.sort((a, b) => {
        // Sort by tab index first, then by position
        if (a.tabIndex !== b.tabIndex) {
          if (a.tabIndex === 0 && b.tabIndex > 0) return 1;
          if (b.tabIndex === 0 && a.tabIndex > 0) return -1;
          return a.tabIndex - b.tabIndex;
        }

        // Then by vertical position (top to bottom)
        if (Math.abs(a.position.y - b.position.y) > 10) {
          return a.position.y - b.position.y;
        }

        // Then by horizontal position (left to right)
        return a.position.x - b.position.x;
      });
    });
  }

  /**
   * Analyze tab order sequence
   */
  private async analyzeTabOrder(page: Page, focusableElements: FocusableElement[]) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const totalChecks = 1;
      let passedChecks = 0;

      // Check for custom tab indices
      const customTabIndices = focusableElements.filter((el) => el.tabIndex > 0);

      if (customTabIndices.length > 0) {
        evidence.push({
          type: 'interaction',
          description: `Found ${customTabIndices.length} elements with custom tab indices`,
          value: `Custom tab indices detected: ${customTabIndices.map((el) => `${el.selector}(${el.tabIndex})`).join(', ')}`,
          severity: 'warning',
        });

        manualReviewItems.push({
          selector: 'elements with tabindex > 0',
          description: 'Review custom tab order implementation for logical flow',
          automatedFindings: `Found ${customTabIndices.length} elements with custom tab indices`,
          reviewRequired:
            'Verify that custom tab order follows logical reading sequence and user workflow',
          priority: 'high',
          estimatedTime: 5,
        });
      } else {
        passedChecks++;
        evidence.push({
          type: 'interaction',
          description: 'No custom tab indices found - using natural DOM order',
          value: `${focusableElements.length} focusable elements follow natural DOM order`,
          severity: 'info',
        });
      }

      // Check for negative tab indices (should be excluded from tab order)
      const negativeTabIndices = await page.$$eval('[tabindex="-1"]', (elements) => {
        return elements.length;
      });

      if (negativeTabIndices > 0) {
        evidence.push({
          type: 'interaction',
          description: `Found ${negativeTabIndices} elements with tabindex="-1" (excluded from tab order)`,
          value: `${negativeTabIndices} elements excluded from tab order`,
          selector: '[tabindex="-1"]',
          severity: 'info',
        });
      }

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };
    } catch (error) {
      evidence.push({
        type: 'text',
        description: 'Error analyzing tab order',
        value: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze tab order'],
        recommendations: ['Check tab order manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze correlation between visual layout and focus order
   */
  private analyzeLayoutCorrelation(focusableElements: FocusableElement[]) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const totalChecks = 1;
      let passedChecks = 0;

      if (focusableElements.length < 2) {
        passedChecks++;
        evidence.push({
          type: 'interaction',
          description: 'Insufficient focusable elements for layout correlation analysis',
          value: `Only ${focusableElements.length} focusable elements found`,
          severity: 'info',
        });

        return {
          totalChecks,
          passedChecks,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      // Group elements by approximate row (within 20px vertical tolerance)
      const rows: FocusableElement[][] = [];

      focusableElements.forEach((element) => {
        const existingRow = rows.find(
          (row) => Math.abs(row[0].position.y - element.position.y) <= 20,
        );

        if (existingRow) {
          existingRow.push(element);
        } else {
          rows.push([element]);
        }
      });

      // Sort elements within each row by horizontal position
      rows.forEach((row) => {
        row.sort((a, b) => a.position.x - b.position.x);
      });

      // Check for potential layout/focus order mismatches
      let layoutIssues = 0;

      for (let i = 0; i < focusableElements.length - 1; i++) {
        const current = focusableElements[i];
        const next = focusableElements[i + 1];

        // Check if next element is significantly to the left or above current
        const isLeftward = next.position.x < current.position.x - 50;
        const isUpward = next.position.y < current.position.y - 20;

        if (isLeftward || isUpward) {
          layoutIssues++;
          evidence.push({
            type: 'interaction',
            description: `Potential focus order issue: element ${i + 2} appears before element ${i + 1} visually`,
            value: `Focus order mismatch detected at ${next.selector}`,
            selector: next.selector,
            severity: 'warning',
          });
        }
      }

      if (layoutIssues === 0) {
        passedChecks++;
        evidence.push({
          type: 'interaction',
          description: 'Focus order appears to follow visual layout',
          value: `${focusableElements.length} focusable elements follow logical visual order`,
          severity: 'info',
        });
      } else {
        issues.push(`Found ${layoutIssues} potential focus order/layout mismatches`);
        recommendations.push('Review focus order to ensure it follows visual layout');
      }

      // Add manual review for complex layouts
      if (rows.length > 3 || focusableElements.length > 10) {
        manualReviewItems.push({
          selector: 'page layout',
          description: 'Review focus order for complex layout with multiple rows/columns',
          automatedFindings: `Found ${layoutIssues} potential layout mismatches in ${rows.length} rows`,
          reviewRequired:
            'Manually verify that focus order follows logical reading sequence across rows and columns',
          priority: 'medium',
          estimatedTime: 7,
        });
      }

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };
    } catch (error) {
      evidence.push({
        type: 'text',
        description: 'Error analyzing layout correlation',
        value: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze layout correlation'],
        recommendations: ['Check layout correlation manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze skip links for navigation efficiency
   */
  private async analyzeSkipLinks(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const skipLinks = await page.$$eval('a[href^="#"]', (links) => {
        return links
          .map((link, index) => ({
            index,
            href: link.getAttribute('href') || '',
            text: link.textContent?.trim() || '',
            isVisible: link.offsetWidth > 0 && link.offsetHeight > 0,
            position: link.getBoundingClientRect(),
          }))
          .filter((link) => {
            const text = link.text.toLowerCase();
            return (
              text.includes('skip') ||
              text.includes('jump') ||
              text.includes('main') ||
              text.includes('content') ||
              text.includes('navigation')
            );
          });
      });

      const totalChecks = 1;
      let passedChecks = 0;

      if (skipLinks.length > 0) {
        passedChecks++;
        evidence.push({
          type: 'interaction',
          description: `Found ${skipLinks.length} potential skip link(s)`,
          value: `Skip links detected: ${skipLinks.map((link) => `"${link.text}" -> ${link.href}`).join(', ')}`,
          selector: 'a[href^="#"]',
          severity: 'info',
        });

        // Add manual review for skip link functionality
        manualReviewItems.push({
          selector: 'skip links',
          description: 'Test skip link functionality and target validity',
          automatedFindings: `Found ${skipLinks.length} potential skip links`,
          reviewRequired: 'Manually test that skip links work correctly and target valid elements',
          priority: 'medium',
          estimatedTime: 3,
        });
      } else {
        evidence.push({
          type: 'interaction',
          description: 'No skip links found - may impact navigation efficiency',
          value: 'No skip links detected on page',
          severity: 'warning',
        });

        recommendations.push('Consider adding skip links for improved navigation');

        manualReviewItems.push({
          selector: 'page navigation',
          description: 'Evaluate if skip links are needed for this page layout',
          automatedFindings: 'No skip links found',
          reviewRequired:
            'Determine if skip links would improve navigation efficiency for this page layout',
          priority: 'low',
          estimatedTime: 2,
        });
      }

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };
    } catch (error) {
      evidence.push({
        type: 'text',
        description: 'Error analyzing skip links',
        value: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze skip links'],
        recommendations: ['Check skip links manually'],
        manualReviewItems,
      };
    }
  }
}
