/**
 * WCAG Rule 20: Motor - 2.5.8
 * 80% Automated - Manual review for complex gesture alternatives
 */

import { Page } from 'puppeteer';
import { ManualReviewConfig, ManualReviewTemplate, ManualReviewItem } from '../utils/manual-review-template';
import { WcagEvidence } from '../types';

export class MotorCheck {
  private checkTemplate = new ManualReviewTemplate();

  /**
   * Perform motor accessibility check - 80% automated
   */
  async performCheck(config: ManualReviewConfig) {
    return this.checkTemplate.executeManualReviewCheck(
      'WCAG-020',
      'Motor',
      'operable',
      0.04,
      'AA',
      0.80, // 80% automation rate
      config,
      this.executeMotorCheck.bind(this)
    );
  }

  /**
   * Execute comprehensive motor accessibility analysis
   */
  private async executeMotorCheck(page: Page, config: ManualReviewConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    // Analyze target sizes
    const targetSizeAnalysis = await this.analyzeTargetSizes(page);
    
    // Analyze gesture requirements
    const gestureAnalysis = await this.analyzeGestureRequirements(page);
    
    // Analyze motion-based controls
    const motionAnalysis = await this.analyzeMotionControls(page);
    
    // Analyze timeout settings
    const timeoutAnalysis = await this.analyzeTimeouts(page);

    // Combine all analyses
    const allAnalyses = [targetSizeAnalysis, gestureAnalysis, motionAnalysis, timeoutAnalysis];

    let totalChecks = 0;
    let passedChecks = 0;
    let manualReviewCount = 0;

    allAnalyses.forEach(analysis => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      manualReviewCount += analysis.manualReviewItems.length;

      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
      manualReviewItems.push(...analysis.manualReviewItems);
    });

    // Calculate automated score
    const automatedScore = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      automatedScore,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
      manualReviewItems,
      automationRate: 80 // 80% automation as specified for Part 5
    };
  }

  /**
   * Analyze target sizes for motor accessibility
   */
  private async analyzeTargetSizes(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const interactiveElements = await page.$$eval('button, a, input, select, textarea, [role="button"], [tabindex]:not([tabindex="-1"])', (elements) => {
        return elements.map((element, index) => {
          const rect = element.getBoundingClientRect();
          const computedStyle = window.getComputedStyle(element);
          
          return {
            index,
            tagName: element.tagName.toLowerCase(),
            width: rect.width,
            height: rect.height,
            area: rect.width * rect.height,
            isVisible: rect.width > 0 && rect.height > 0 && computedStyle.visibility !== 'hidden',
            hasText: (element.textContent?.trim().length || 0) > 0,
            type: (element as HTMLInputElement).type || undefined,
          };
        }).filter(el => el.isVisible);
      });

      let totalChecks = interactiveElements.length;
      let passedChecks = 0;

      const minTargetSize = 44; // 44px minimum for AA compliance

      interactiveElements.forEach((element, index) => {
        const meetsMinSize = element.width >= minTargetSize && element.height >= minTargetSize;
        
        if (meetsMinSize) {
          passedChecks++;
          evidence.push({
            type: 'measurement',
            description: `Interactive element ${index + 1} meets minimum target size`,
            value: `Size: ${element.width}x${element.height}px, Meets minimum: true`,
            element: element.tagName,
          });
        } else {
          issues.push(`Interactive element ${index + 1} below minimum target size (${element.width}x${element.height}px)`);
          evidence.push({
            type: 'measurement',
            description: `Interactive element ${index + 1} may be too small for motor accessibility`,
            value: `Size: ${element.width}x${element.height}px, Required: ${minTargetSize}x${minTargetSize}px, Meets minimum: false`,
            element: element.tagName,
          });
          recommendations.push(`Increase size of ${element.tagName} element ${index + 1} to at least ${minTargetSize}x${minTargetSize}px`);
        }
      });

      // Add manual review for spacing and layout
      if (interactiveElements.length > 5) {
        manualReviewItems.push({
          selector: 'interactive elements',
          description: 'Review spacing between interactive elements for motor accessibility',
          automatedFindings: `Found ${interactiveElements.length} interactive elements, ${interactiveElements.filter(el => el.width < minTargetSize || el.height < minTargetSize).length} below minimum size`,
          reviewRequired: 'Verify adequate spacing and touch target sizes for motor accessibility',
          priority: 'medium',
          estimatedTime: 5,
          element: 'interactive elements',
          context: `Elements: ${interactiveElements.length}, Small targets: ${interactiveElements.filter(el => el.width < minTargetSize || el.height < minTargetSize).length}`,
        });
      }

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'measurement',
        description: 'Error analyzing target sizes',
        value: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        element: 'interactive elements',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze target sizes'],
        recommendations: ['Check target sizes manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze gesture requirements
   */
  private async analyzeGestureRequirements(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const gestureElements = await page.evaluate(() => {
        const elements = document.querySelectorAll('*');
        const gestureElements: any[] = [];

        elements.forEach((element, index) => {
          const hasComplexGesture = element.hasAttribute('ontouchstart') || 
                                   element.hasAttribute('ontouchmove') || 
                                   element.className.includes('swipe') ||
                                   element.className.includes('pinch') ||
                                   element.className.includes('rotate');

          const hasSimpleAlternative = element.hasAttribute('onclick') || 
                                      element.hasAttribute('onmousedown') ||
                                      element.tagName.toLowerCase() === 'button' ||
                                      element.tagName.toLowerCase() === 'a';

          if (hasComplexGesture) {
            gestureElements.push({
              index,
              tagName: element.tagName.toLowerCase(),
              className: element.className,
              hasComplexGesture,
              hasSimpleAlternative,
              gestureType: element.className.includes('swipe') ? 'swipe' :
                          element.className.includes('pinch') ? 'pinch' :
                          element.className.includes('rotate') ? 'rotate' : 'touch',
            });
          }
        });

        return gestureElements;
      });

      let totalChecks = gestureElements.length;
      let passedChecks = 0;

      if (gestureElements.length === 0) {
        evidence.push({
          type: 'interaction',
          description: 'No complex gesture requirements detected',
          value: 'Complex gestures found: false',
          element: 'page',
        });
        return {
          totalChecks: 1,
          passedChecks: 1,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      gestureElements.forEach((element, index) => {
        if (element.hasSimpleAlternative) {
          passedChecks++;
          evidence.push({
            type: 'interaction',
            description: `Gesture element ${index + 1} has simple alternative`,
            value: `Gesture type: ${element.gestureType}, Has simple alternative: true`,
            element: element.tagName,
          });
        } else {
          issues.push(`Gesture element ${index + 1} lacks simple alternative`);
          evidence.push({
            type: 'interaction',
            description: `Gesture element ${index + 1} may need simple alternative`,
            value: `Gesture type: ${element.gestureType}, Has simple alternative: false`,
            element: element.tagName,
          });
          recommendations.push(`Add simple click/tap alternative for ${element.gestureType} gesture`);
        }

        // Add manual review for all gesture elements
        manualReviewItems.push({
          selector: element.tagName,
          description: `Test gesture element ${index + 1} for simple alternative functionality`,
          automatedFindings: `Element ${index + 1}: Gesture type: ${element.gestureType}, Has simple alternative: ${element.hasSimpleAlternative}`,
          reviewRequired: 'Test that gesture functionality has simple click/tap alternative',
          priority: 'high',
          estimatedTime: 3,
          element: element.tagName,
          context: `Element ${index + 1}: Gesture type: ${element.gestureType}, Has simple alternative: ${element.hasSimpleAlternative}`,
        });
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'interaction',
        description: 'Error analyzing gesture requirements',
        value: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        element: 'gesture elements',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze gesture requirements'],
        recommendations: ['Check gesture requirements manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze motion-based controls
   */
  private async analyzeMotionControls(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const motionElements = await page.evaluate(() => {
        // Look for elements that might use device motion
        const motionIndicators = [
          'shake',
          'tilt',
          'motion',
          'accelerometer',
          'gyroscope',
          'devicemotion',
          'deviceorientation',
        ];

        const elements = document.querySelectorAll('*');
        const motionElements: any[] = [];

        elements.forEach((element, index) => {
          const text = element.textContent?.toLowerCase() || '';
          const className = element.className.toLowerCase();
          const hasMotionIndicator = motionIndicators.some(indicator => 
            text.includes(indicator) || className.includes(indicator)
          );

          const hasMotionEvent = element.hasAttribute('ondevicemotion') || 
                                 element.hasAttribute('ondeviceorientation');

          if (hasMotionIndicator || hasMotionEvent) {
            motionElements.push({
              index,
              tagName: element.tagName.toLowerCase(),
              hasMotionIndicator,
              hasMotionEvent,
              text: text.substring(0, 100),
            });
          }
        });

        return motionElements;
      });

      let totalChecks = motionElements.length;
      let passedChecks = 0;

      if (motionElements.length === 0) {
        evidence.push({
          type: 'interaction',
          description: 'No motion-based controls detected',
          value: 'Motion controls found: false',
          element: 'page',
        });
        return {
          totalChecks: 1,
          passedChecks: 1,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      motionElements.forEach((element, index) => {
        // All motion controls require manual review
        manualReviewItems.push({
          selector: element.tagName,
          description: `Verify motion control ${index + 1} has non-motion alternative`,
          automatedFindings: `Element ${index + 1}: Has motion event: ${element.hasMotionEvent}, Text: ${element.text}`,
          reviewRequired: 'Test that motion control has non-motion alternative (buttons, keyboard, etc.)',
          priority: 'high',
          estimatedTime: 4,
          element: element.tagName,
          context: `Element ${index + 1}: Has motion event: ${element.hasMotionEvent}, Text: ${element.text}`,
        });

        evidence.push({
          type: 'interaction',
          description: `Motion control ${index + 1} requires manual verification`,
          value: `Has motion event: ${element.hasMotionEvent}, Requires manual check: true`,
          element: element.tagName,
        });
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'interaction',
        description: 'Error analyzing motion controls',
        value: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        element: 'motion elements',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze motion controls'],
        recommendations: ['Check motion controls manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze timeout settings for motor accessibility
   */
  private async analyzeTimeouts(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const timeoutElements = await page.evaluate(() => {
        // Look for timeout-related elements
        const timeoutIndicators = [
          'timeout',
          'session',
          'expire',
          'countdown',
          'timer',
          'time limit',
        ];

        const elements = document.querySelectorAll('*');
        const timeoutElements: Array<{
          index: number;
          tagName: string;
          text: string;
          hasExtendOption: boolean;
        }> = [];

        elements.forEach((element, index) => {
          const text = element.textContent?.toLowerCase() || '';
          const hasTimeoutIndicator = timeoutIndicators.some((indicator) =>
            text.includes(indicator),
          );

          if (hasTimeoutIndicator) {
            timeoutElements.push({
              index,
              tagName: element.tagName.toLowerCase(),
              text: text.substring(0, 100),
              hasExtendOption: text.includes('extend') || text.includes('more time'),
            });
          }
        });

        // Check for meta refresh
        const metaRefresh = document.querySelector('meta[http-equiv="refresh"]');
        if (metaRefresh) {
          timeoutElements.push({
            index: -1,
            tagName: 'meta',
            text: 'Meta refresh detected',
            hasExtendOption: false,
          });
        }

        return timeoutElements;
      });

      const totalChecks = timeoutElements.length;
      let passedChecks = 0;

      if (timeoutElements.length === 0) {
        evidence.push({
          type: 'interaction',
          description: 'No timeout indicators found',
          value: 'Timeouts found: false',
          element: 'page',
        });
        return {
          totalChecks: 1,
          passedChecks: 1,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      timeoutElements.forEach((element, index) => {
        if (element.hasExtendOption) {
          passedChecks++;
          evidence.push({
            type: 'interaction',
            description: `Timeout element ${index + 1} appears to have extend option`,
            value: `Has extend option: true, Text: ${element.text}`,
            element: element.tagName,
          });
        } else {
          evidence.push({
            type: 'interaction',
            description: `Timeout element ${index + 1} may need extend option`,
            value: `Has extend option: false, Text: ${element.text}`,
            element: element.tagName,
          });
        }

        // Add manual review for all timeout elements
        manualReviewItems.push({
          selector: element.tagName,
          description: `Verify timeout ${index + 1} allows sufficient time or extension for motor disabilities`,
          automatedFindings: `Element ${index + 1}: ${element.text}`,
          reviewRequired: 'Verify timeout allows sufficient time or extension for motor disabilities',
          priority: 'high',
          estimatedTime: 3,
          element: element.tagName,
          context: `Element ${index + 1}, Has extend option: ${element.hasExtendOption}, Text: ${element.text}`,
        });
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'interaction',
        description: 'Error analyzing timeouts',
        value: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        element: 'timeout elements',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze timeouts'],
        recommendations: ['Check timeout settings manually'],
        manualReviewItems,
      };
    }
  }
}
