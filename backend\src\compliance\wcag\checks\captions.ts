/**
 * WCAG Rule 2: Captions - 1.2.2
 * 80% Automated - Manual review for caption accuracy and synchronization
 */

import { Page } from 'puppeteer';
import {
  ManualReviewConfig,
  ManualReviewTemplate,
  ManualReviewItem,
} from '../utils/manual-review-template';
import { WcagEvidence } from '../types';

export class CaptionsCheck {
  private checkTemplate = new ManualReviewTemplate();

  /**
   * Perform captions check - 80% automated
   */
  async performCheck(config: ManualReviewConfig) {
    return this.checkTemplate.executeManualReviewCheck(
      'WCAG-002',
      'Captions',
      'perceivable',
      0.08,
      'A',
      0.8, // 80% automation rate
      config,
      this.executeCaptionsCheck.bind(this),
    );
  }

  /**
   * Execute comprehensive captions analysis
   */
  private async executeCaptionsCheck(page: Page, config: ManualReviewConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    // Analyze video elements
    const videoAnalysis = await this.analyzeVideoElements(page);

    // Analyze audio elements
    const audioAnalysis = await this.analyzeAudioElements(page);

    // Check for caption files
    const captionFileAnalysis = await this.analyzeCaptionFiles(page);

    // Check for embedded captions
    const embeddedCaptionAnalysis = await this.analyzeEmbeddedCaptions(page);

    // Combine all analyses
    const allAnalyses = [
      videoAnalysis,
      audioAnalysis,
      captionFileAnalysis,
      embeddedCaptionAnalysis,
    ];

    let totalChecks = 0;
    let passedChecks = 0;
    let manualReviewCount = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      manualReviewCount += analysis.manualReviewItems.length;

      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
      manualReviewItems.push(...analysis.manualReviewItems);
    });

    // Calculate automated score
    const automatedScore = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      automatedScore,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
      manualReviewItems,
      automationRate: 80, // 80% automation as specified for Part 5
    };
  }

  /**
   * Analyze video elements for caption requirements
   */
  private async analyzeVideoElements(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const videoElements = await page.$$eval('video', (videos) => {
        return videos.map((video, index) => ({
          index,
          src: video.src || '',
          poster: video.poster || '',
          controls: video.hasAttribute('controls'),
          autoplay: video.hasAttribute('autoplay'),
          muted: video.hasAttribute('muted'),
          tracks: Array.from(video.querySelectorAll('track')).map((track) => ({
            kind: track.getAttribute('kind') || '',
            src: track.getAttribute('src') || '',
            srclang: track.getAttribute('srclang') || '',
            label: track.getAttribute('label') || '',
            default: track.hasAttribute('default'),
          })),
          duration: video.duration || 0,
          hasAudio: !video.muted,
        }));
      });

      const totalChecks = videoElements.length;
      let passedChecks = 0;

      videoElements.forEach((video, index) => {
        const captionTracks = video.tracks.filter(
          (track) => track.kind === 'captions' || track.kind === 'subtitles',
        );

        if (video.hasAudio && video.duration > 0) {
          if (captionTracks.length > 0) {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: `Video ${index + 1} has ${captionTracks.length} caption track(s)`,
              value: `video[src="${video.src}"] - tracks: ${captionTracks.length}, duration: ${video.duration}s`,
              selector: `video[src="${video.src}"]`,
              severity: 'info',
            });

            // Add manual review for caption accuracy
            manualReviewItems.push({
              selector: `video[src="${video.src}"]`,
              description: `Verify caption accuracy and synchronization for video ${index + 1}`,
              automatedFindings: `Video with audio content found, caption tracks detected`,
              reviewRequired: `Manual verification of caption accuracy, timing, and completeness`,
              priority: 'high',
              estimatedTime: 5,
              context: `Video: ${video.src}, Caption tracks: ${captionTracks.length}, Duration: ${video.duration}s`,
            });
          } else {
            issues.push(`Video ${index + 1} with audio content lacks caption tracks`);
            evidence.push({
              type: 'code',
              description: `Video ${index + 1} requires captions for accessibility`,
              value: `video[src="${video.src}"] - hasAudio: ${video.hasAudio}, duration: ${video.duration}, captionTracks: 0`,
            });
            recommendations.push(`Add caption tracks to video ${index + 1}`);
          }
        } else {
          // Video without audio or zero duration
          passedChecks++;
          evidence.push({
            type: 'text',
            description: `Video ${index + 1} does not require captions (no audio or zero duration)`,
            value: `video[src="${video.src}"] - hasAudio: ${video.hasAudio}, duration: ${video.duration}`,
          });
        }
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };
    } catch (error) {
      evidence.push({
        type: 'text',
        description: 'Error analyzing video elements for captions',
        value: `video - error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze video elements'],
        recommendations: ['Check video elements manually for caption requirements'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze audio elements for transcript requirements
   */
  private async analyzeAudioElements(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const audioElements = await page.$$eval('audio', (audios) => {
        return audios.map((audio, index) => ({
          index,
          src: audio.src || '',
          controls: audio.hasAttribute('controls'),
          autoplay: audio.hasAttribute('autoplay'),
          muted: audio.hasAttribute('muted'),
          duration: audio.duration || 0,
        }));
      });

      const totalChecks = audioElements.length;
      let passedChecks = 0;

      audioElements.forEach((audio, index) => {
        if (audio.duration > 0 && !audio.muted) {
          // Audio content requires transcript
          manualReviewItems.push({
            selector: `audio[src="${audio.src}"]`,
            description: `Verify transcript availability for audio ${index + 1}`,
            automatedFindings: `Audio content detected with duration ${audio.duration}s`,
            reviewRequired: `Manual verification that transcript is available and accurate`,
            priority: 'high',
            estimatedTime: 3,
            context: `Audio: ${audio.src}, Duration: ${audio.duration}s`,
          });

          evidence.push({
            type: 'text',
            description: `Audio ${index + 1} requires manual verification for transcript`,
            value: `Audio duration: ${audio.duration}s, requires transcript verification`,
            selector: `audio[src="${audio.src}"]`,
            severity: 'warning',
          });
        } else {
          passedChecks++;
          evidence.push({
            type: 'text',
            description: `Audio ${index + 1} does not require transcript (muted or zero duration)`,
            value: `audio[src="${audio.src}"] - duration: ${audio.duration}, muted: ${audio.muted}`,
          });
        }
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };
    } catch (error) {
      evidence.push({
        type: 'text',
        description: 'Error analyzing audio elements for transcripts',
        value: `audio - error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze audio elements'],
        recommendations: ['Check audio elements manually for transcript requirements'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze caption files (WebVTT, SRT, etc.)
   */
  private async analyzeCaptionFiles(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const trackElements = await page.$$eval('track', (tracks) => {
        return tracks.map((track, index) => ({
          index,
          kind: track.getAttribute('kind') || '',
          src: track.getAttribute('src') || '',
          srclang: track.getAttribute('srclang') || '',
          label: track.getAttribute('label') || '',
          default: track.hasAttribute('default'),
        }));
      });

      const totalChecks = trackElements.length;
      let passedChecks = 0;

      for (const track of trackElements) {
        if (track.kind === 'captions' || track.kind === 'subtitles') {
          if (track.src) {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: `Caption track found: ${track.label || track.srclang || 'unlabeled'}`,
              value: `track[src="${track.src}"] - kind: ${track.kind}, language: ${track.srclang}, label: ${track.label}, default: ${track.default}`,
            });

            // Add manual review for caption file quality
            manualReviewItems.push({
              selector: `track[src="${track.src}"]`,
              description: `Review caption file quality and format: ${track.src}`,
              automatedFindings: `Caption track found with kind: ${track.kind}, language: ${track.srclang}`,
              reviewRequired: `Manual verification of caption file quality, timing accuracy, and completeness`,
              priority: 'medium',
              estimatedTime: 3,
              context: `Caption: ${track.src}, Language: ${track.srclang}, Kind: ${track.kind}`,
            });
          } else {
            issues.push(`Caption track ${track.index + 1} missing src attribute`);
            evidence.push({
              type: 'code',
              description: `Caption track ${track.index + 1} has no source file`,
              value: `track[kind="${track.kind}"] - kind: ${track.kind}`,
              severity: 'error',
            });
          }
        }
      }

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };
    } catch (error) {
      evidence.push({
        type: 'text',
        description: 'Error analyzing caption files',
        value: `track - error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze caption files'],
        recommendations: ['Check caption files manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze embedded captions in video content
   */
  private async analyzeEmbeddedCaptions(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      // Check for common video platforms with embedded captions
      const embeddedVideos = await page.$$eval('iframe', (iframes) => {
        return iframes
          .map((iframe, index) => ({
            index,
            src: iframe.src || '',
            title: iframe.title || '',
          }))
          .filter((iframe) => {
            const src = iframe.src.toLowerCase();
            return (
              src.includes('youtube') ||
              src.includes('vimeo') ||
              src.includes('wistia') ||
              src.includes('brightcove')
            );
          });
      });

      const totalChecks = embeddedVideos.length;
      const passedChecks = 0;

      embeddedVideos.forEach((video, index) => {
        // Embedded videos require manual verification
        manualReviewItems.push({
          selector: `iframe[src*="${video.src.split('/')[2]}"]`,
          description: `Verify embedded video captions: ${video.title || `Video ${index + 1}`}`,
          automatedFindings: `Embedded video detected from ${video.src.split('/')[2]}`,
          reviewRequired: `Manual verification that embedded video has captions enabled and accessible`,
          priority: 'high',
          estimatedTime: 4,
        });

        evidence.push({
          type: 'interaction',
          description: `Embedded video requires manual caption verification: ${video.title || `Video ${index + 1}`}`,
          value: `iframe[src="${video.src}"] - platform: ${this.detectVideoPlatform(video.src)}, requiresManualCheck: true`,
          severity: 'warning',
        });
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };
    } catch (error) {
      evidence.push({
        type: 'text',
        description: 'Error analyzing embedded captions',
        value: `iframe - error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze embedded captions'],
        recommendations: ['Check embedded videos manually for captions'],
        manualReviewItems,
      };
    }
  }

  /**
   * Detect video platform from URL
   */
  private detectVideoPlatform(src: string): string {
    const url = src.toLowerCase();
    if (url.includes('youtube')) return 'YouTube';
    if (url.includes('vimeo')) return 'Vimeo';
    if (url.includes('wistia')) return 'Wistia';
    if (url.includes('brightcove')) return 'Brightcove';
    return 'Unknown';
  }
}
