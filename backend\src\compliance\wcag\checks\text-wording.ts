/**
 * WCAG Rule 18: Text and Wording - 3.1.5
 * 75% Automated - Manual review for context and domain-specific terminology
 */

import { Page } from 'puppeteer';
import { ManualReviewConfig, ManualReviewTemplate, ManualReviewItem } from '../utils/manual-review-template';
import { WcagEvidence } from '../types';
const fleschKincaid = require('flesch-kincaid');
const { syllable } = require('syllable');
const natural = require('natural');
const compromise = require('compromise');
const Sentiment = require('sentiment');

interface TextAnalysis {
  text: string;
  wordCount: number;
  sentenceCount: number;
  syllableCount: number;
  fleschScore: number;
  fleschGrade: number;
  complexWords: string[];
  readabilityLevel: string;
  sentiment: any;
  entities: string[];
}

export class TextWordingCheck {
  private checkTemplate = new ManualReviewTemplate();
  private sentiment = new Sentiment();

  /**
   * Perform text and wording check - 75% automated
   */
  async performCheck(config: ManualReviewConfig) {
    return this.checkTemplate.executeManualReviewCheck(
      'WCAG-018',
      'Text and Wording',
      'understandable',
      0.07,
      'AAA',
      0.75, // 75% automation rate
      config,
      this.executeTextWordingCheck.bind(this)
    );
  }

  /**
   * Execute comprehensive text and wording analysis
   */
  private async executeTextWordingCheck(page: Page, config: ManualReviewConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    // Analyze main content text
    const contentAnalysis = await this.analyzeContentText(page);
    
    // Analyze form labels and instructions
    const formAnalysis = await this.analyzeFormText(page);
    
    // Analyze error messages
    const errorAnalysis = await this.analyzeErrorMessages(page);
    
    // Analyze navigation and UI text
    const uiAnalysis = await this.analyzeUIText(page);

    // Combine all analyses
    const allAnalyses = [contentAnalysis, formAnalysis, errorAnalysis, uiAnalysis];

    let totalChecks = 0;
    let passedChecks = 0;
    let manualReviewCount = 0;

    allAnalyses.forEach(analysis => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      manualReviewCount += analysis.manualReviewItems.length;

      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
      manualReviewItems.push(...analysis.manualReviewItems);
    });

    // Calculate automated score
    const automatedScore = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      automatedScore,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
      manualReviewItems,
      automationRate: 75 // 75% automation as specified for Part 5
    };
  }

  /**
   * Analyze main content text for readability
   */
  private async analyzeContentText(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const contentText = await page.evaluate(() => {
        // Get main content areas
        const contentSelectors = [
          'main',
          '[role="main"]',
          'article',
          '.content',
          '#content',
          '.main-content',
          'body',
        ];

        let content = '';
        for (const selector of contentSelectors) {
          const element = document.querySelector(selector);
          if (element) {
            // Remove script and style elements
            const clone = element.cloneNode(true) as Element;
            clone.querySelectorAll('script, style, nav, header, footer').forEach(el => el.remove());
            content = clone.textContent || '';
            break;
          }
        }

        return content.trim();
      });

      if (!contentText || contentText.length < 50) {
        evidence.push({
          type: 'warning',
          description: 'Insufficient content text for analysis',
          value: `Text length: ${contentText.length}`,
          element: 'main content',
        });

        return {
          totalChecks: 1,
          passedChecks: 1,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      const analysis = this.analyzeText(contentText);
      let totalChecks = 3;
      let passedChecks = 0;

      // Check readability level
      if (analysis.fleschScore >= 60) {
        passedChecks++;
        evidence.push({
          type: 'info',
          description: `Content readability is acceptable (Flesch score: ${analysis.fleschScore})`,
          value: `Flesch score: ${analysis.fleschScore}, Level: ${analysis.readabilityLevel}, Grade: ${analysis.fleschGrade}`,
          element: 'main content',
        });
      } else {
        issues.push(`Content readability may be too difficult (Flesch score: ${analysis.fleschScore})`);
        recommendations.push('Consider simplifying language and sentence structure');
        
        evidence.push({
          type: 'warning',
          description: `Content may be difficult to read (Flesch score: ${analysis.fleschScore})`,
          value: `Flesch score: ${analysis.fleschScore}, Level: ${analysis.readabilityLevel}, Grade: ${analysis.fleschGrade}`,
          element: 'main content',
        });
      }

      // Check for complex words
      if (analysis.complexWords.length <= analysis.wordCount * 0.1) {
        passedChecks++;
        evidence.push({
          type: 'info',
          description: `Complex word usage is reasonable (${analysis.complexWords.length}/${analysis.wordCount})`,
          value: `Complex words: ${analysis.complexWords.length}, Total words: ${analysis.wordCount}, Percentage: ${Math.round((analysis.complexWords.length / analysis.wordCount) * 100)}%`,
          element: 'main content',
        });
      } else {
        issues.push(`High complex word usage: ${analysis.complexWords.length}/${analysis.wordCount}`);
        recommendations.push('Consider using simpler alternatives for complex words');
        
        evidence.push({
          type: 'warning',
          description: `High complex word usage detected`,
          value: `Complex words: ${analysis.complexWords.length}, Total words: ${analysis.wordCount}, Sample: ${analysis.complexWords.slice(0, 10).join(', ')}`,
          element: 'main content',
        });
      }

      // Check sentence length
      const avgSentenceLength = analysis.wordCount / analysis.sentenceCount;
      if (avgSentenceLength <= 20) {
        passedChecks++;
        evidence.push({
          type: 'info',
          description: `Average sentence length is appropriate (${avgSentenceLength.toFixed(1)} words)`,
          value: `Average length: ${avgSentenceLength.toFixed(1)} words, Sentence count: ${analysis.sentenceCount}`,
          element: 'main content',
        });
      } else {
        issues.push(`Long average sentence length: ${avgSentenceLength.toFixed(1)} words`);
        recommendations.push('Consider breaking long sentences into shorter ones');
        
        evidence.push({
          type: 'warning',
          description: `Sentences may be too long on average`,
          value: `Average length: ${avgSentenceLength.toFixed(1)} words, Sentence count: ${analysis.sentenceCount}`,
          element: 'main content',
        });
      }

      // Add manual review for domain-specific content
      if (analysis.entities.length > 0 || analysis.complexWords.length > 5) {
        manualReviewItems.push({
          selector: 'main content',
          description: 'Review domain-specific terminology and technical language for clarity',
          automatedFindings: `Complex words: ${analysis.complexWords.length}, Entities: ${analysis.entities.length}, Flesch score: ${analysis.fleschScore}`,
          reviewRequired: 'Review domain-specific terminology and technical language for clarity and accessibility',
          priority: 'medium',
          estimatedTime: 8,
          type: 'domain_terminology',
          element: 'main content',
          context: `Complex words: ${analysis.complexWords.length}, Entities: ${analysis.entities.length}, Flesch score: ${analysis.fleschScore}`,
        });
      }

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'error',
        description: 'Error analyzing content text',
        value: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        element: 'main content',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze content text'],
        recommendations: ['Check content readability manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze form labels and instructions
   */
  private async analyzeFormText(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const formTexts = await page.$$eval('form', (forms) => {
        return forms.map((form, index) => {
          const labels = Array.from(form.querySelectorAll('label')).map(label => label.textContent?.trim() || '');
          const instructions = Array.from(form.querySelectorAll('.instruction, .help-text, [role="note"]'))
            .map(el => el.textContent?.trim() || '');
          const placeholders = Array.from(form.querySelectorAll('input[placeholder], textarea[placeholder]'))
            .map(el => el.getAttribute('placeholder') || '');

          return {
            index,
            labels: labels.filter(text => text.length > 0),
            instructions: instructions.filter(text => text.length > 0),
            placeholders: placeholders.filter(text => text.length > 0),
          };
        });
      });

      let totalChecks = formTexts.length;
      let passedChecks = 0;

      formTexts.forEach((form, index) => {
        const allTexts = [...form.labels, ...form.instructions, ...form.placeholders];
        
        if (allTexts.length === 0) {
          passedChecks++;
          evidence.push({
            type: 'info',
            description: `Form ${index + 1} has no text to analyze`,
            value: 'Has text: false',
            element: `form:nth-of-type(${index + 1})`,
          });
          return;
        }

        const combinedText = allTexts.join(' ');
        const analysis = this.analyzeText(combinedText);

        if (analysis.fleschScore >= 70 || combinedText.length < 100) {
          passedChecks++;
          evidence.push({
            type: 'info',
            description: `Form ${index + 1} text is clear and understandable`,
            value: `Flesch score: ${analysis.fleschScore}, Text length: ${combinedText.length}`,
            element: `form:nth-of-type(${index + 1})`,
          });
        } else {
          issues.push(`Form ${index + 1} text may be difficult to understand`);
          evidence.push({
            type: 'warning',
            description: `Form ${index + 1} text may need simplification`,
            value: `Flesch score: ${analysis.fleschScore}, Complex words: ${analysis.complexWords.slice(0, 5).join(', ')}`,
            element: `form:nth-of-type(${index + 1})`,
          });

          manualReviewItems.push({
            selector: `form:nth-of-type(${index + 1})`,
            description: `Review form ${index + 1} text for clarity and simplicity`,
            automatedFindings: `Form ${index + 1}: Flesch score: ${analysis.fleschScore}, Complex words: ${analysis.complexWords.slice(0, 5).join(', ')}`,
            reviewRequired: 'Review form text for clarity and simplicity to ensure accessibility',
            priority: 'high',
            estimatedTime: 4,
            type: 'form_text_clarity',
            element: `form:nth-of-type(${index + 1})`,
            context: `Form ${index + 1}: Flesch score: ${analysis.fleschScore}, Complex words: ${analysis.complexWords.slice(0, 5).join(', ')}`,
          });
        }
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'error',
        description: 'Error analyzing form text',
        value: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        element: 'form',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze form text'],
        recommendations: ['Check form text manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze error messages for clarity
   */
  private async analyzeErrorMessages(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const errorMessages = await page.$$eval('.error, .alert-danger, [role="alert"], .validation-error', (elements) => {
        return elements.map((el, index) => ({
          index,
          text: el.textContent?.trim() || '',
          isVisible: (el as HTMLElement).offsetWidth > 0 && (el as HTMLElement).offsetHeight > 0,
        })).filter(msg => msg.text.length > 0);
      });

      let totalChecks = errorMessages.length || 1;
      let passedChecks = 0;

      if (errorMessages.length === 0) {
        passedChecks++;
        evidence.push({
          type: 'info',
          description: 'No error messages found to analyze',
          value: 'Error messages found: false',
          element: 'page',
        });
      } else {
        errorMessages.forEach((error, index) => {
          const analysis = this.analyzeText(error.text);
          
          if (analysis.fleschScore >= 80 || error.text.length < 50) {
            passedChecks++;
            evidence.push({
              type: 'info',
              description: `Error message ${index + 1} is clear and understandable`,
              value: `Message: ${error.text}, Flesch score: ${analysis.fleschScore}`,
              element: '.error, .alert-danger, [role="alert"]',
            });
          } else {
            issues.push(`Error message ${index + 1} may be unclear`);
            evidence.push({
              type: 'warning',
              description: `Error message ${index + 1} may need simplification`,
              value: `Message: ${error.text}, Flesch score: ${analysis.fleschScore}`,
              selector: '.error, .alert-danger, [role="alert"]',
            });
          }
        });

        // Add manual review for error message effectiveness
        manualReviewItems.push({
          selector: 'error messages',
          description: 'Review error messages for clarity and actionable guidance',
          automatedFindings: `Found ${errorMessages.length} error messages: ${errorMessages.map(msg => msg.text).join(', ')}`,
          reviewRequired: 'Review error messages for clarity and actionable guidance to help users',
          priority: 'high',
          estimatedTime: 3,
          type: 'error_message_clarity',
          element: 'error messages',
          context: `Error messages count: ${errorMessages.length}, Messages: ${errorMessages.map(msg => msg.text).join(', ')}`,
        });
      }

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'error',
        description: 'Error analyzing error messages',
        value: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        element: 'error messages',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze error messages'],
        recommendations: ['Check error messages manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze UI text (buttons, links, navigation)
   */
  private async analyzeUIText(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const uiTexts = await page.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button')).map(btn => btn.textContent?.trim() || '');
        const links = Array.from(document.querySelectorAll('a')).map(link => link.textContent?.trim() || '');
        const navItems = Array.from(document.querySelectorAll('nav a, .nav a, .menu a')).map(item => item.textContent?.trim() || '');

        return {
          buttons: buttons.filter(text => text.length > 0),
          links: links.filter(text => text.length > 0),
          navItems: navItems.filter(text => text.length > 0),
        };
      });

      let totalChecks = 1;
      let passedChecks = 0;

      const allUITexts = [...uiTexts.buttons, ...uiTexts.links, ...uiTexts.navItems];
      
      // Check for vague or unclear UI text
      const vagueTerms = ['click here', 'read more', 'learn more', 'here', 'more', 'link', 'button'];
      const vagueTexts = allUITexts.filter(text => 
        vagueTerms.some(term => text.toLowerCase().includes(term))
      );

      if (vagueTexts.length === 0) {
        passedChecks++;
        evidence.push({
          type: 'info',
          description: 'UI text appears descriptive and clear',
          value: `Total UI texts: ${allUITexts.length}, Vague texts found: 0`,
          selector: 'buttons, links, navigation',
        });
      } else {
        issues.push(`Found ${vagueTexts.length} potentially vague UI text elements`);
        recommendations.push('Use more descriptive text for buttons and links');
        
        evidence.push({
          type: 'warning',
          description: 'Found potentially vague UI text',
          value: `Vague texts count: ${vagueTexts.length}, Examples: ${vagueTexts.slice(0, 5).join(', ')}`,
          element: 'buttons, links, navigation',
        });

        manualReviewItems.push({
          selector: 'buttons, links, navigation',
          description: 'Review UI text for descriptiveness and clarity',
          automatedFindings: `Found ${vagueTexts.length} vague UI texts out of ${allUITexts.length} total: ${vagueTexts.join(', ')}`,
          reviewRequired: 'Review UI text for descriptiveness and clarity to improve accessibility',
          priority: 'medium',
          estimatedTime: 5,
          type: 'ui_text_clarity',
          element: 'buttons, links, navigation',
          context: `Vague texts: ${vagueTexts.length}, Total UI texts: ${allUITexts.length}, Examples: ${vagueTexts.join(', ')}`,
        });
      }

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'error',
        description: 'Error analyzing UI text',
        value: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        element: 'UI elements',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze UI text'],
        recommendations: ['Check UI text manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze text using NLP and readability metrics
   */
  private analyzeText(text: string): TextAnalysis {
    const sentences = natural.SentenceTokenizer.tokenize(text);
    const words = natural.WordTokenizer.tokenize(text.toLowerCase());
    const syllableCount = words.reduce((total: number, word: string) => total + syllable(word), 0);
    
    const fleschScore = fleschKincaid({
      sentence: sentences.length,
      word: words.length,
      syllable: syllableCount,
    });

    // Determine reading level
    let readabilityLevel = 'Very Difficult';
    if (fleschScore >= 90) readabilityLevel = 'Very Easy';
    else if (fleschScore >= 80) readabilityLevel = 'Easy';
    else if (fleschScore >= 70) readabilityLevel = 'Fairly Easy';
    else if (fleschScore >= 60) readabilityLevel = 'Standard';
    else if (fleschScore >= 50) readabilityLevel = 'Fairly Difficult';
    else if (fleschScore >= 30) readabilityLevel = 'Difficult';

    // Find complex words (3+ syllables)
    const complexWords = words.filter((word: string) => syllable(word) >= 3);

    // Calculate grade level
    const fleschGrade = 0.39 * (words.length / sentences.length) + 11.8 * (syllableCount / words.length) - 15.59;

    // Analyze sentiment
    const sentimentResult = this.sentiment.analyze(text);

    // Extract entities using compromise
    const doc = compromise(text);
    const entities = doc.people().out('array').concat(doc.places().out('array'));

    return {
      text,
      wordCount: words.length,
      sentenceCount: sentences.length,
      syllableCount,
      fleschScore: Math.round(fleschScore * 10) / 10,
      fleschGrade: Math.round(fleschGrade * 10) / 10,
      complexWords: [...new Set(complexWords)] as string[], // Remove duplicates
      readabilityLevel,
      sentiment: sentimentResult,
      entities: [...new Set(entities)] as string[], // Remove duplicates
    };
  }
}
