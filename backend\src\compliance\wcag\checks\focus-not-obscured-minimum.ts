/**
 * WCAG Rule 10: Focus Not Obscured (Minimum) - 2.4.11
 * 100% Automated - No manual review required
 */

import { Page } from 'puppeteer';
import { CheckConfig, CheckTemplate } from '../utils/check-template';
import { LayoutAnalyzer } from '../utils/layout-analyzer';
import { FocusTracker } from '../utils/focus-tracker';
import { WcagEvidence } from '../types';

export class FocusNotObscuredMinimumCheck {
  private checkTemplate = new CheckTemplate();

  /**
   * Perform focus not obscured minimum check - 100% automated
   */
  async performCheck(config: CheckConfig) {
    return this.checkTemplate.executeCheck(
      'WCAG-010',
      'Focus Not Obscured (Minimum)',
      'operable',
      0.06,
      'AA',
      config,
      this.executeFocusNotObscuredCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );
  }

  /**
   * Execute focus obstruction analysis
   */
  private async executeFocusNotObscuredCheck(page: Page, config: CheckConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Get all focusable elements
    const focusableElements = await FocusTracker.getFocusableElements(page);

    let totalElements = 0;
    let passedElements = 0;

    // Test each focusable element for obstruction
    for (const element of focusableElements) {
      totalElements++;

      // Focus the element
      await page.focus(element.selector);

      // Check if element is obscured
      const obscurationResult = await LayoutAnalyzer.checkElementObscured(page, element.selector);

      if (!obscurationResult.isObscured) {
        passedElements++;

        evidence.push({
          type: 'measurement',
          description: 'Focused element is not obscured by other content',
          value: 'Element remains fully visible when focused',
          selector: element.selector,
          severity: 'info',
        });
      } else {
        issues.push(
          `Focused element ${element.selector} is obscured by: ${obscurationResult.obscuringElements.join(', ')}`,
        );

        evidence.push({
          type: 'measurement',
          description: 'Focused element is obscured by other content',
          value: `Obscured by: ${obscurationResult.obscuringElements.join(', ')}`,
          selector: element.selector,
          severity: 'error',
        });

        recommendations.push(
          `Ensure ${element.selector} is not hidden by fixed/sticky elements when focused`,
        );
      }
    }

    // Calculate score
    const score = totalElements > 0 ? Math.round((passedElements / totalElements) * 100) : 100;

    // Add summary evidence
    evidence.unshift({
      type: 'text',
      description: 'Focus obstruction analysis summary',
      value: `${passedElements}/${totalElements} focused elements remain unobscured`,
      severity: score >= 90 ? 'info' : score >= 70 ? 'warning' : 'error',
    });

    if (score < 100) {
      recommendations.unshift(
        'Review fixed and sticky positioned elements that may obscure focused content',
      );
      recommendations.push('Consider using scroll-padding or scroll-margin CSS properties');
      recommendations.push('Ensure focused elements are scrolled into view when needed');
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }
}
